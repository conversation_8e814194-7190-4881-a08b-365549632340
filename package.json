{"name": "whatsapp-llm-bot", "version": "1.0.0", "description": "A WhatsApp bot that responds to mentions with LLM-generated content", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "tsx src/index.js", "dev2": "<PERSON>ra dev", "start:v2": "tsx src/v2/index.ts", "dev:v2": "tsx watch src/v2/index.ts", "build:v2": "tsc -p tsconfig.json", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.23", "@google/genai": "^1.12.0", "@mastra/core": "^0.10.15", "@mastra/fastembed": "^0.10.1", "@mastra/libsql": "^0.10.3", "@mastra/loggers": "^0.10.5", "@mastra/memory": "^0.10.4", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/sdk-node": "^0.203.0", "ai": "^4.3.19", "chalk": "^4.1.2", "dotenv": "^17.2.1", "express": "^5.1.0", "https-proxy-agent": "^7.0.6", "lamejs": "github:zhuker/lamejs", "langfuse-vercel": "^3.38.4", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "openai-gpt-token-counter": "^1.1.1", "puppeteer": "^24.15.0", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "whatsapp-web.js": "^1.31.0", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "^2.1.3", "@types/node": "^24.1.0", "mastra": "^0.10.16", "@types/qrcode": "^1.5.5", "@types/qrcode-terminal": "^0.12.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}