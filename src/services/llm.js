const logger = require("../utils/logger");
const {
  getPersonaPrompt,
  getTuringPrompt,
  getTuringPrompt2,
  getTuringPrompt3,
  getPromptByTemplate,
} = require("../prompts/turingPrompt");
const { generateText } = require("./generateText");
const { configService } = require("./configService");

const DISABLE_CONTEXT = false;

/**
 * Generates a response using the Vercel AI SDK with Groq/Gemini
 * @param {string} prompt - The user's question or prompt
 * @param {Array} context - Array of previous messages for context (optional)
 * @param {Buffer} imageBuffer - Image data as Buffer (optional, for vision models)
 * @param {Buffer} audioBuffer - Audio data as Buffer (optional, for audio processing)
 * @param {string} audioMimeType - MIME type of the audio file (optional)
 * @param {string} userId - WhatsApp user ID for tracking (optional)
 * @param {string} conversationId - WhatsApp chat ID for tracking (optional)
 * @returns {Promise<string>} - The generated response
 */
const generateResponse = async (
  prompt,
  contextArray = [],
  imageBuffer = null,
  audioBuffer = null,
  audioMimeType = null,
  userName = null,
  userId = null,
  conversationId = null
) => {
  if (!process.env.GROQ_API_KEY && !process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    throw new Error(
      "Groq or Gemini API key is not set. Please set GROQ_API_KEY or GOOGLE_GENERATIVE_AI_API_KEY in your .env file."
    );
  }

  try {
    // Get current configuration
    const botName = configService.get("botName");
    const promptTemplate = configService.get("promptTemplate");
    const customPrompt = configService.get("customPrompt");
    const context = DISABLE_CONTEXT ? [] : contextArray;

    const systemPrompt = getPromptByTemplate(
      promptTemplate,
      botName,
      customPrompt
    );

    // Build messages array with context
    const messages = [];

    // Add context messages if provided
    if (context && context.length > 0) {
      const contextMessages = context.map((msg) => ({
        role: msg.fromMe ? "assistant" : "user",
        name: msg.sender,
        content: msg.content,
        metadata: {
          userId: msg.userId,
        },
      }));
      messages.push(...contextMessages);
    }

    // Add the current user message (with optional image and/or audio)
    if (imageBuffer || audioBuffer) {
      // Create multimodal message with text and media
      const contentParts = [
        {
          type: "text",
          text: prompt,
        },
      ];

      if (imageBuffer) {
        contentParts.push({
          type: "image",
          image: imageBuffer,
        });
      }

      if (audioBuffer) {
        contentParts.push({
          type: "file",
          data: audioBuffer,
          mimeType: audioMimeType,
        });
      }

      messages.push({
        role: "user",
        name: userName,
        content: contentParts,
        metadata: {
          userId,
        },
      });
    } else {
      // Text-only message
      messages.push({
        role: "user",
        name: userName,
        content: prompt,
        metadata: {
          userId: userName,
        },
      });
    }

    const text = await generateText(messages, systemPrompt, conversationId);

    return text.trim();
  } catch (error) {
    logger.error("Error generating response from LLM:", error);
    throw new Error("Failed to generate a response. Please try again later.");
  }
};

/**
 * Generates a summary of chat messages using the LLM
 * @param {string} messagesText - Formatted string of messages to summarise
 * @param {number} messageCount - Number of messages being summarised
 * @returns {Promise<string>} - The generated summary
 */
const generateSummary = async (messagesText, messageCount) => {
  if (!process.env.GROQ_API_KEY && !process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    throw new Error(
      "Groq or Gemini API key is not set. Please set GROQ_API_KEY or GOOGLE_GENERATIVE_AI_API_KEY in your .env file."
    );
  }

  try {
    const summaryPrompt = `Please provide a concise summary of the following ${messageCount} WhatsApp chat messages. Focus on:
- Key topics discussed
- Important decisions or conclusions
- Main participants and their contributions
- Any action items or follow-ups mentioned

Here are the messages to summarise:

${messagesText}

Please provide a clear, structured summary that captures the essence of the conversation.`;

    // Build messages array for summarisation
    const messages = [
      {
        role: "system",
        content:
          "You are a helpful assistant that specializes in summarizing conversations. Provide clear, concise summaries that capture the key points and context of discussions.",
      },
      {
        role: "user",
        content: summaryPrompt,
      },
    ];

    const text = await generateText(messages);

    return text.trim();
  } catch (error) {
    logger.error("Error generating summary from LLM:", error);
    throw new Error("Failed to generate a summary. Please try again later.");
  }
};

/**
 * Generates short memories from chat messages for storage in a memory bank
 * @param {string} messagesText - Formatted string of messages to create memories from
 * @param {number} messageCount - Number of messages being processed
 * @returns {Promise<string>} - The generated memories
 */
const generateMemories = async (messagesText, messageCount) => {
  if (!process.env.GROQ_API_KEY && !process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    throw new Error(
      "Groq or Gemini API key is not set. Please set GROQ_API_KEY or GOOGLE_GENERATIVE_AI_API_KEY in your .env file."
    );
  }

  try {
    const memoriesPrompt = `Please analyze the following ${messageCount} chat messages from a WhatsApp group chat and create short, memorable facts and insights for a memory bank.

Create 10 general bullet points focusing on:
- Important personal information about participants (interests, preferences, life events)
- Key decisions, agreements, or commitments made
- Recurring topics or themes in the conversation
- Significant events or milestones mentioned
- Useful context that would be valuable to remember for future conversations

Then create 5 bullet points for each individual participant focusing on:
- Key points they mentioned or contributed to the conversation
- Insights into their personality, interests, or quirks based on their contributions
- Significant events or milestones mentioned by them

Ignore any messages sent by 'Nonce' or 'Meta AI' (the AI chat bots)

Format your response as a list of concise memory items, each on a new line starting with "- ". Keep each memory item under 100 characters and focus on factual, useful information.

Here are the messages to analyze:

${messagesText}

Please provide clear, actionable memories that would be useful for future reference.`;

    // Build messages array for memory creation
    const messages = [
      {
        role: "system",
        content:
          "You are a helpful assistant that specializes in extracting and organizing important information from conversations into memorable facts. Create concise, factual memories that capture key information about people, events, and decisions.",
      },
      {
        role: "user",
        content: memoriesPrompt,
      },
    ];

    const text = await generateText(messages);

    return text.trim();
  } catch (error) {
    logger.error("Error generating memories from LLM:", error);
    throw new Error("Failed to generate memories. Please try again later.");
  }
};

module.exports = {
  generateResponse,
  generateSummary,
  generateMemories,
};
